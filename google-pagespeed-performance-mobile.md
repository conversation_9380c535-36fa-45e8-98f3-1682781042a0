Metrics Expand view First Contentful Paint 1.8 s Largest Contentful Paint 3.5 s
Total Blocking Time 200 ms Cumulative Layout Shift 0.003 Speed Index 4.9 s
Captured at Jun 12, 2025, 1:35 AM GMT+3 Emulated Moto G Power with Lighthouse
12.6.1 Single page session Initial page load Slow 4G throttling Using
HeadlessChromium 136.0.7103.113 with lr

Show audits relevant to:

All

FCP

LCP

TBT

CLS Insights Improve image delivery Est savings of 718 KiB Reducing the download
time of images can improve the perceived load time of the page and LCP. Learn
more about optimizing image sizeLCPFCP URL Resource Size Est Savings ponyclub.gr
1st party 449.8 KiB 329.9 KiB /images/hero-image.webp(www.ponyclub.gr) 178.9 KiB
133.0 KiB Increasing the image compression factor could improve this image's
download size. 100.1 KiB This image file is larger than it needs to be (630x768)
for its displayed dimensions (613x460). Use responsive images to reduce the
image download size. 74.8 KiB
/_next/image?url=%2Fimages%2Fhero-image.webp&w=750&q=85(www.ponyclub.gr) 118.0
KiB 75.7 KiB Increasing the image compression factor could improve this image's
download size. 75.7 KiB
/\_next/image?url=%2Fimages%2Fround2.jpg&w=384&q=75(www.ponyclub.gr) 40.0 KiB
33.3 KiB Increasing the image compression factor could improve this image's
download size. 27.7 KiB This image file is larger than it needs to be (384x196)
for its displayed dimensions (165x248). Use responsive images to reduce the
image download size. 18.3 KiB
/\_next/image?url=%2Fimages%2Fponyclub_logo.png&w=384&q=75(www.ponyclub.gr) 27.9
KiB 25.6 KiB Increasing the image compression factor could improve this image's
download size. 22.6 KiB This image file is larger than it needs to be (384x84)
for its displayed dimensions (184x76). Use responsive images to reduce the image
download size. 15.7 KiB
/\_next/image?url=%2Fimages%2Fround1.jpg&w=384&q=75(www.ponyclub.gr) 27.3 KiB
24.8 KiB Increasing the image compression factor could improve this image's
download size. 15.2 KiB This image file is larger than it needs to be (384x194)
for its displayed dimensions (165x95). Use responsive images to reduce the image
download size. 21.5 KiB
/\_next/image?url=%2Fimages%2Fround3.jpg&w=384&q=75(www.ponyclub.gr) 23.7 KiB
20.4 KiB Increasing the image compression factor could improve this image's
download size. 11.4 KiB This image file is larger than it needs to be (384x197)
for its displayed dimensions (165x124). Use responsive images to reduce the
image download size. 17.3 KiB
/\_next/image?url=%2Fimages%2FRafting….jpg&w=384&q=75(www.ponyclub.gr) 24.4 KiB
11.6 KiB Increasing the image compression factor could improve this image's
download size. 11.6 KiB
/\_next/image?url=%2Fimages%2Fround2.jpg&w=160&q=75(www.ponyclub.gr) 9.6 KiB 5.5
KiB Increasing the image compression factor could improve this image's download
size. 5.5 KiB Other Google APIs/SDKs utility 393.5 KiB 388.1 KiB
/a-/ALV-UjXo6…=s120-c-rp-mo-br100(lh3.googleusercontent.com) 31.1 KiB 30.9 KiB
Using a modern image format (WebP, AVIF) or increasing the image compression
could improve this image's download size. 28.8 KiB This image file is larger
than it needs to be (120x120) for its displayed dimensions (40x40). Use
responsive images to reduce the image download size. 27.7 KiB
/a-/ALV-UjWQS…=s120-c-rp-mo-ba2-br100(lh3.googleusercontent.com) 30.2 KiB 29.9
KiB Using a modern image format (WebP, AVIF) or increasing the image compression
could improve this image's download size. 27.8 KiB This image file is larger
than it needs to be (120x120) for its displayed dimensions (40x40). Use
responsive images to reduce the image download size. 26.8 KiB
/a-/ALV-UjXA4…=s120-c-rp-mo-br100(lh3.googleusercontent.com) 28.8 KiB 28.5 KiB
Using a modern image format (WebP, AVIF) or increasing the image compression
could improve this image's download size. 26.4 KiB This image file is larger
than it needs to be (120x120) for its displayed dimensions (40x40). Use
responsive images to reduce the image download size. 25.6 KiB
/a-/ALV-UjUCi…=s120-c-rp-mo-br100(lh3.googleusercontent.com) 28.8 KiB 28.5 KiB
Using a modern image format (WebP, AVIF) or increasing the image compression
could improve this image's download size. 26.4 KiB This image file is larger
than it needs to be (120x120) for its displayed dimensions (40x40). Use
responsive images to reduce the image download size. 25.6 KiB
/a-/ALV-UjXor…=s120-c-rp-mo-ba3-br100(lh3.googleusercontent.com) 27.6 KiB 27.4
KiB Using a modern image format (WebP, AVIF) or increasing the image compression
could improve this image's download size. 25.3 KiB This image file is larger
than it needs to be (120x120) for its displayed dimensions (40x40). Use
responsive images to reduce the image download size. 24.5 KiB
/a-/ALV-UjUFq…=s120-c-rp-mo-br100(lh3.googleusercontent.com) 25.6 KiB 25.4 KiB
Using a modern image format (WebP, AVIF) or increasing the image compression
could improve this image's download size. 23.3 KiB This image file is larger
than it needs to be (120x120) for its displayed dimensions (40x40). Use
responsive images to reduce the image download size. 22.8 KiB
/a-/ALV-UjVJL…=s120-c-rp-mo-ba3-br100(lh3.googleusercontent.com) 25.5 KiB 25.3
KiB Using a modern image format (WebP, AVIF) or increasing the image compression
could improve this image's download size. 23.2 KiB This image file is larger
than it needs to be (120x120) for its displayed dimensions (40x40). Use
responsive images to reduce the image download size. 22.7 KiB
/a-/ALV-UjVXn…=s120-c-rp-mo-ba4-br100(lh3.googleusercontent.com) 24.0 KiB 23.7
KiB Using a modern image format (WebP, AVIF) or increasing the image compression
could improve this image's download size. 21.6 KiB This image file is larger
than it needs to be (120x120) for its displayed dimensions (40x40). Use
responsive images to reduce the image download size. 21.3 KiB
/a-/ALV-UjVOB…=s120-c-rp-mo-ba3-br100(lh3.googleusercontent.com) 23.1 KiB 22.8
KiB Using a modern image format (WebP, AVIF) or increasing the image compression
could improve this image's download size. 20.7 KiB This image file is larger
than it needs to be (120x120) for its displayed dimensions (40x40). Use
responsive images to reduce the image download size. 20.5 KiB
/a-/ALV-UjW51…=s120-c-rp-mo-br100(lh3.googleusercontent.com) 22.8 KiB 22.6 KiB
Using a modern image format (WebP, AVIF) or increasing the image compression
could improve this image's download size. 20.5 KiB This image file is larger
than it needs to be (120x120) for its displayed dimensions (40x40). Use
responsive images to reduce the image download size. 20.3 KiB
/a-/ALV-UjVLJ…=s120-c-rp-mo-br100(lh3.googleusercontent.com) 21.4 KiB 21.2 KiB
Using a modern image format (WebP, AVIF) or increasing the image compression
could improve this image's download size. 19.1 KiB This image file is larger
than it needs to be (120x120) for its displayed dimensions (40x40). Use
responsive images to reduce the image download size. 19.0 KiB
/a-/ALV-UjVxz…=s120-c-rp-mo-ba4-br100(lh3.googleusercontent.com) 21.0 KiB 20.7
KiB Using a modern image format (WebP, AVIF) or increasing the image compression
could improve this image's download size. 18.6 KiB This image file is larger
than it needs to be (120x120) for its displayed dimensions (40x40). Use
responsive images to reduce the image download size. 18.6 KiB
/a-/ALV-UjUA5…=s120-c-rp-mo-ba3-br100(lh3.googleusercontent.com) 18.9 KiB 18.6
KiB Using a modern image format (WebP, AVIF) or increasing the image compression
could improve this image's download size. 16.5 KiB This image file is larger
than it needs to be (120x120) for its displayed dimensions (40x40). Use
responsive images to reduce the image download size. 16.8 KiB
/a-/ALV-UjXXV…=s120-c-rp-mo-ba2-br100(lh3.googleusercontent.com) 15.9 KiB 15.6
KiB Using a modern image format (WebP, AVIF) or increasing the image compression
could improve this image's download size. 13.5 KiB This image file is larger
than it needs to be (120x120) for its displayed dimensions (40x40). Use
responsive images to reduce the image download size. 14.1 KiB
/a/ACg8ocKJ9…=s120-c-rp-mo-ba3-br100(lh3.googleusercontent.com) 7.6 KiB 7.4 KiB
Using a modern image format (WebP, AVIF) or increasing the image compression
could improve this image's download size. 5.3 KiB This image file is larger than
it needs to be (120x120) for its displayed dimensions (40x40). Use responsive
images to reduce the image download size. 6.8 KiB
/a/ACg8ocK2B…=s120-c-rp-mo-ba4-br100(lh3.googleusercontent.com) 7.5 KiB 7.3 KiB
Using a modern image format (WebP, AVIF) or increasing the image compression
could improve this image's download size. 5.2 KiB This image file is larger than
it needs to be (120x120) for its displayed dimensions (40x40). Use responsive
images to reduce the image download size. 6.7 KiB
/a/ACg8ocIX4…=s120-c-rp-mo-ba2-br100(lh3.googleusercontent.com) 7.5 KiB 7.3 KiB
Using a modern image format (WebP, AVIF) or increasing the image compression
could improve this image's download size. 5.2 KiB This image file is larger than
it needs to be (120x120) for its displayed dimensions (40x40). Use responsive
images to reduce the image download size. 6.7 KiB
/a/ACg8ocKTf…=s120-c-rp-mo-ba3-br100(lh3.googleusercontent.com) 7.4 KiB 7.1 KiB
Using a modern image format (WebP, AVIF) or increasing the image compression
could improve this image's download size. 5.1 KiB This image file is larger than
it needs to be (120x120) for its displayed dimensions (40x40). Use responsive
images to reduce the image download size. 6.6 KiB
/a/ACg8ocJlt…=s120-c-rp-mo-ba2-br100(lh3.googleusercontent.com) 7.2 KiB 6.9 KiB
Using a modern image format (WebP, AVIF) or increasing the image compression
could improve this image's download size. 4.8 KiB This image file is larger than
it needs to be (120x120) for its displayed dimensions (40x40). Use responsive
images to reduce the image download size. 6.4 KiB
/a/ACg8ocIDs…=s120-c-rp-mo-ba3-br100(lh3.googleusercontent.com) 7.0 KiB 6.8 KiB
Using a modern image format (WebP, AVIF) or increasing the image compression
could improve this image's download size. 4.7 KiB This image file is larger than
it needs to be (120x120) for its displayed dimensions (40x40). Use responsive
images to reduce the image download size. 6.3 KiB
/a/ACg8ocIlm…=s120-c-rp-mo-br100(lh3.googleusercontent.com) 4.6 KiB 4.3 KiB This
image file is larger than it needs to be (120x120) for its displayed dimensions
(40x40). Use responsive images to reduce the image download size. 4.1 KiB Legacy
JavaScript Est savings of 76 KiB Polyfills and transforms enable older browsers
to use new JavaScript features. However, many aren't necessary for modern
browsers. Consider modifying your JavaScript build process to not transpile
Baseline features, unless you know you must support older browsers. Learn why
most sites can deploy ES6+ code without transpilingLCPFCP URL Wasted bytes
ponyclub.gr 1st party 76.4 KiB …chunks/797-1d16e539206e578b.js(www.ponyclub.gr)
42.9 KiB …chunks/797-1d16e539206e578b.js:18:70632(www.ponyclub.gr)
Array.prototype.at …chunks/797-1d16e539206e578b.js:18:70020(www.ponyclub.gr)
Array.prototype.flat …chunks/797-1d16e539206e578b.js:18:70133(www.ponyclub.gr)
Array.prototype.flatMap
…chunks/797-1d16e539206e578b.js:18:70509(www.ponyclub.gr) Object.fromEntries
…chunks/797-1d16e539206e578b.js:18:70767(www.ponyclub.gr) Object.hasOwn
…chunks/797-1d16e539206e578b.js:18:69762(www.ponyclub.gr)
String.prototype.trimEnd
…chunks/797-1d16e539206e578b.js:18:69677(www.ponyclub.gr)
String.prototype.trimStart …chunks/7f358c6e-b3c55055b0d02466.js(www.ponyclub.gr)
33.5 KiB …chunks/7f358c6e-b3c55055b0d02466.js:1:52368(www.ponyclub.gr)
Array.from Forced reflow Many APIs, typically reading layout geometry, force the
rendering engine to pause script execution in order to calculate the style and
layout. Learn more about forced reflow and its mitigations. Top function call
Total reflow time …chunks/797-1d16e539206e578b.js:19:63174(www.ponyclub.gr) 63
ms Source Total reflow time
…chunks/7f358c6e-b3c55055b0d02466.js:1:11233(www.ponyclub.gr) 5 ms
[unattributed] 11 ms …chunks/945-7b4c98ba143510df.js:1:31735(www.ponyclub.gr) 65
ms …chunks/7f358c6e-b3c55055b0d02466.js:1:9388(www.ponyclub.gr) 9 ms
/BokunWidgets.7edd992….js:2:20861(static.bokun.io) 0 ms
/BokunWidgets.7edd992….js:2:32099(static.bokun.io) 2 ms LCP request discovery
Optimize LCP by making the LCP image discoverable from the HTML immediately, and
avoiding lazy-loadingLCP lazy load not applied fetchpriority=high should be
applied Request is discoverable in initial document main.relative >
div.relative > div.absolute > video.absolute
<video src="/images/hero-video.mp4" poster="/images/hero-image.webp" autoplay="" muted="" loop="" playsinline="" preload="metadata" class="absolute inset-0 z-10 h-full w-full object-cover">
Use efficient cache lifetimes Est savings of 441 KiB A long cache lifetime can
speed up repeat visits to your page. Learn more.LCPFCP Request Cache TTL
Transfer Size Google Maps utility 241 KiB
…api/js?key=AIzaSyB9j…(maps.googleapis.com) 30m 222 KiB
…api/js?client=…(maps.googleapis.com) 30m 11 KiB
…js/StaticMapService.GetMapImage?…(maps.googleapis.com) 1d 8 KiB Other Google
APIs/SDKs utility 199 KiB
/a-/ALV-UjXo6…=s120-c-rp-mo-br100(lh3.googleusercontent.com) 1d 13 KiB
/a-/ALV-UjWQS…=s120-c-rp-mo-ba2-br100(lh3.googleusercontent.com) 1d 12 KiB
/a-/ALV-UjWBx…=s120-c-rp-mo-br100(lh3.googleusercontent.com) 1d 12 KiB
/a-/ALV-UjXA4…=s120-c-rp-mo-br100(lh3.googleusercontent.com) 1d 12 KiB
/a-/ALV-UjUCi…=s120-c-rp-mo-br100(lh3.googleusercontent.com) 1d 12 KiB
/a-/ALV-UjXor…=s120-c-rp-mo-ba3-br100(lh3.googleusercontent.com) 1d 11 KiB
/a-/ALV-UjUFq…=s120-c-rp-mo-br100(lh3.googleusercontent.com) 1d 10 KiB
/a-/ALV-UjVJL…=s120-c-rp-mo-ba3-br100(lh3.googleusercontent.com) 1d 10 KiB
/a-/ALV-UjVXn…=s120-c-rp-mo-ba4-br100(lh3.googleusercontent.com) 1d 10 KiB
/a-/ALV-UjVOB…=s120-c-rp-mo-ba3-br100(lh3.googleusercontent.com) 1d 9 KiB
/a-/ALV-UjW51…=s120-c-rp-mo-br100(lh3.googleusercontent.com) 1d 9 KiB
/a-/ALV-UjVxb…=s120-c-rp-mo-br100(lh3.googleusercontent.com) 1d 9 KiB
/a-/ALV-UjVLJ…=s120-c-rp-mo-br100(lh3.googleusercontent.com) 1d 9 KiB
/a-/ALV-UjVxz…=s120-c-rp-mo-ba4-br100(lh3.googleusercontent.com) 1d 9 KiB
/a-/ALV-UjUA5…=s120-c-rp-mo-ba3-br100(lh3.googleusercontent.com) 1d 8 KiB
/a-/ALV-UjXXV…=s120-c-rp-mo-ba2-br100(lh3.googleusercontent.com) 1d 7 KiB
/a/ACg8ocKJ9…=s120-c-rp-mo-ba3-br100(lh3.googleusercontent.com) 1d 3 KiB
/a/ACg8ocK2B…=s120-c-rp-mo-ba4-br100(lh3.googleusercontent.com) 1d 3 KiB
/a/ACg8ocIX4…=s120-c-rp-mo-ba2-br100(lh3.googleusercontent.com) 1d 3 KiB
/a/ACg8ocKTf…=s120-c-rp-mo-ba3-br100(lh3.googleusercontent.com) 1d 3 KiB
/a/ACg8ocJlt…=s120-c-rp-mo-ba2-br100(lh3.googleusercontent.com) 1d 3 KiB
/a/ACg8ocIDs…=s120-c-rp-mo-ba3-br100(lh3.googleusercontent.com) 1d 3 KiB
/a/ACg8ocIlm…=s120-c-rp-mo-br100(lh3.googleusercontent.com) 1d 2 KiB
/a/ACg8ocIpK…=s120-c-rp-mo-br100(lh3.googleusercontent.com) 1d 2 KiB
/a/ACg8ocIi_…=s120-c-rp-mo-br100(lh3.googleusercontent.com) 1d 2 KiB
/a/ACg8ocKlz…=s120-c-rp-mo-br100(lh3.googleusercontent.com) 1d 2 KiB
/a/ACg8ocKmV…=s120-c-rp-mo-br100(lh3.googleusercontent.com) 1d 2 KiB
/a/ACg8ocLvN…=s120-c-rp-mo-br100(lh3.googleusercontent.com) 1d 2 KiB
/a/ACg8ocIOh…=s120-c-rp-mo-br100(lh3.googleusercontent.com) 1d 2 KiB
/a/ACg8ocIvu…=s120-c-rp-mo-br100(lh3.googleusercontent.com) 1d 2 KiB
/a/ACg8ocJcB…=s120-c-rp-mo-br100(lh3.googleusercontent.com) 1d 2 KiB
/a/ACg8ocKxL…=s120-c-rp-mo-br100(lh3.googleusercontent.com) 1d 1 KiB bokun.io 1
KiB …flags/gb.png(widgets.bokun.io) 1h 1 KiB Layout shift culprits LCP by phase
3rd parties These insights are also available in the Chrome DevTools Performance
Panel - record a trace to view more detailed information. Diagnostics Defer
offscreen images Est savings of 236 KiB Consider lazy-loading offscreen and
hidden images after all critical resources have finished loading to lower time
to interactive. Learn how to defer offscreen images.LCPFCP URL Resource Size Est
Savings Other Google APIs/SDKs utility 235.8 KiB 235.8 KiB Google reviewer
profile picture 9
<img class="css-1pelb8y" src="https://lh3.googleusercontent.com/a-/ALV-UjXo6A0q597qeUH5nP8wmpHXaL01yNc2w…" alt="Google reviewer profile picture 9">
/a-/ALV-UjXo6…=s120-c-rp-mo-br100(lh3.googleusercontent.com) 31.1 KiB 31.1 KiB
Google reviewer profile picture 31
<img class="css-1pelb8y" src="https://lh3.googleusercontent.com/a-/ALV-UjWBxT2rgteH4h0v7ZwHM9jDZghBMlhU1…" alt="Google reviewer profile picture 31">
/a-/ALV-UjWBx…=s120-c-rp-mo-br100(lh3.googleusercontent.com) 29.0 KiB 29.0 KiB
Google reviewer profile picture 3
<img class="css-1pelb8y" src="https://lh3.googleusercontent.com/a-/ALV-UjXA4lsc4ituHxMAxvrdHK-XwdxjRlCit…" alt="Google reviewer profile picture 3">
/a-/ALV-UjXA4…=s120-c-rp-mo-br100(lh3.googleusercontent.com) 28.8 KiB 28.8 KiB
Google reviewer profile picture 7
<img class="css-1pelb8y" src="https://lh3.googleusercontent.com/a-/ALV-UjUCiFUPvbQr4SYi0bxi2lnkbQJNAncDe…" alt="Google reviewer profile picture 7">
/a-/ALV-UjUCi…=s120-c-rp-mo-br100(lh3.googleusercontent.com) 28.8 KiB 28.8 KiB
Google reviewer profile picture 6
<img class="css-1pelb8y" src="https://lh3.googleusercontent.com/a-/ALV-UjUFqFjr8jtBHk8zAsmHseMEAX8XLKAvi…" alt="Google reviewer profile picture 6">
/a-/ALV-UjUFq…=s120-c-rp-mo-br100(lh3.googleusercontent.com) 25.6 KiB 25.6 KiB
Google reviewer profile picture 10
<img class="css-1pelb8y" src="https://lh3.googleusercontent.com/a-/ALV-UjVXnaHftxvHzl3-RyVWJTz_E1BYSnf0G…" alt="Google reviewer profile picture 10">
/a-/ALV-UjVXn…=s120-c-rp-mo-ba4-br100(lh3.googleusercontent.com) 24.0 KiB 24.0
KiB Google reviewer profile picture 11
<img class="css-1pelb8y" src="https://lh3.googleusercontent.com/a-/ALV-UjVLJINSBJn2Ltd65Ha8OYWOv34di0VN7…" alt="Google reviewer profile picture 11">
/a-/ALV-UjVLJ…=s120-c-rp-mo-br100(lh3.googleusercontent.com) 21.4 KiB 21.4 KiB
Google reviewer profile picture 1
<img class="css-1pelb8y" src="https://lh3.googleusercontent.com/a-/ALV-UjUA5N5C4d-LLl2YDed1dwtswJD5_5B6s…" alt="Google reviewer profile picture 1">
/a-/ALV-UjUA5…=s120-c-rp-mo-ba3-br100(lh3.googleusercontent.com) 18.9 KiB 18.9
KiB Google reviewer profile picture 12
<img class="css-1pelb8y" src="https://lh3.googleusercontent.com/a-/ALV-UjXXVjaCWo3i5mEsXKs0aHHbiQ_NEj1It…" alt="Google reviewer profile picture 12">
/a-/ALV-UjXXV…=s120-c-rp-mo-ba2-br100(lh3.googleusercontent.com) 15.9 KiB 15.9
KiB Google reviewer profile picture 8
<img class="css-1pelb8y" src="https://lh3.googleusercontent.com/a/ACg8ocIi_P7n75neOi2T_W6nRs2_VTIOtKBVuR…" alt="Google reviewer profile picture 8">
/a/ACg8ocIi\_…=s120-c-rp-mo-br100(lh3.googleusercontent.com) 4.4 KiB 4.4 KiB
Google reviewer profile picture 32
<img class="css-1pelb8y" src="https://lh3.googleusercontent.com/a/ACg8ocKlzpWOlPBsWu79xmPTiNx6pAGR8IE5hA…" alt="Google reviewer profile picture 32">
/a/ACg8ocKlz…=s120-c-rp-mo-br100(lh3.googleusercontent.com) 4.3 KiB 4.3 KiB
Google reviewer profile picture 2
<img class="css-1pelb8y" src="https://lh3.googleusercontent.com/a/ACg8ocJcBpyGTl_gDhTURTmpD3DB6dKPEUE3Vc…" alt="Google reviewer profile picture 2">
/a/ACg8ocJcB…=s120-c-rp-mo-br100(lh3.googleusercontent.com) 3.5 KiB 3.5 KiB
Avoid serving legacy JavaScript to modern browsers Est savings of 24 KiB
Polyfills and transforms enable legacy browsers to use new JavaScript features.
However, many aren't necessary for modern browsers. Consider modifying your
JavaScript build process to not transpile Baseline features, unless you know you
must support legacy browsers. Learn why most sites can deploy ES6+ code without
transpilingLCPFCP URL Est Savings ponyclub.gr 1st party 24.3 KiB
…chunks/797-1d16e539206e578b.js(www.ponyclub.gr) 13.0 KiB
…chunks/797-1d16e539206e578b.js:18:70632(www.ponyclub.gr) Array.prototype.at
…chunks/797-1d16e539206e578b.js:18:70020(www.ponyclub.gr) Array.prototype.flat
…chunks/797-1d16e539206e578b.js:18:70133(www.ponyclub.gr)
Array.prototype.flatMap
…chunks/797-1d16e539206e578b.js:18:70509(www.ponyclub.gr) Object.fromEntries
…chunks/797-1d16e539206e578b.js:18:70767(www.ponyclub.gr) Object.hasOwn
…chunks/797-1d16e539206e578b.js:18:69762(www.ponyclub.gr)
String.prototype.trimEnd
…chunks/797-1d16e539206e578b.js:18:69677(www.ponyclub.gr)
String.prototype.trimStart …chunks/7f358c6e-b3c55055b0d02466.js(www.ponyclub.gr)
11.0 KiB …chunks/7f358c6e-b3c55055b0d02466.js:1:52368(www.ponyclub.gr)
Array.from …chunks/945-7b4c98ba143510df.js(www.ponyclub.gr) 0.3 KiB
…chunks/945-7b4c98ba143510df.js:1:46621(www.ponyclub.gr)
@babel/plugin-transform-classes Reduce unused JavaScript Est savings of 234 KiB
Reduce unused JavaScript and defer loading scripts until they are required to
decrease bytes consumed by network activity. Learn how to reduce unused
JavaScript.LCPFCP URL Transfer Size Est Savings ponyclub.gr 1st party 155.7 KiB
83.8 KiB …chunks/797-1d16e539206e578b.js(www.ponyclub.gr) 116.6 KiB 49.8 KiB
…chunks/507-8a62939ad235c32b.js(www.ponyclub.gr) 39.1 KiB 34.0 KiB Google Tag
Manager tag-manager 148.2 KiB 79.6 KiB
/gtag/js?id=G-6J3ELVNTQE(www.googletagmanager.com) 148.2 KiB 79.6 KiB bokun.io
138.8 KiB 71.0 KiB /BokunWidgets.7edd992….js(static.bokun.io) 138.8 KiB 71.0 KiB
Avoid enormous network payloads Total size was 5,030 KiB Large network payloads
cost users real money and are highly correlated with long load times. Learn how
to reduce payload sizes. URL Transfer Size ponyclub.gr 1st party 2,348.9 KiB
/images/hero-video.mp4(www.ponyclub.gr) 1,933.0 KiB
/images/hero-image.webp(www.ponyclub.gr) 179.7 KiB
/\_next/image?url=%2Fimages%2Fhero-image.webp&w=750&q=85(www.ponyclub.gr) 118.7
KiB …chunks/797-1d16e539206e578b.js(www.ponyclub.gr) 117.5 KiB bokun.io 297.9
KiB /BokunWidgets.7edd992….js(static.bokun.io) 139.4 KiB
/OnlineSal….5cbfb12….js(static.bokun.io) 86.4 KiB
/46929.fb94355….js(static.bokun.io) 72.1 KiB Google Maps utility 161.6 KiB
…api/js?key=AIzaSyB9j…(maps.googleapis.com) 87.0 KiB
…4a/util.js(maps.googleapis.com) 74.5 KiB Google Tag Manager tag-manager 149.2
KiB /gtag/js?id=G-6J3ELVNTQE(www.googletagmanager.com) 149.2 KiB Avoid long
main-thread tasks 6 long tasks found Lists the longest tasks on the main thread,
useful for identifying worst contributors to input delay. Learn how to avoid
long main-thread tasksTBT URL Start Time Duration ponyclub.gr 1st party 442 ms
…chunks/797-1d16e539206e578b.js(www.ponyclub.gr) 9,901 ms 193 ms
…chunks/797-1d16e539206e578b.js(www.ponyclub.gr) 11,042 ms 182 ms
…chunks/797-1d16e539206e578b.js(www.ponyclub.gr) 14,556 ms 67 ms bokun.io 187 ms
/BokunWidgets.7edd992….js(static.bokun.io) 11,229 ms 135 ms
/BokunWidgets.7edd992….js(static.bokun.io) 10,952 ms 52 ms Google Tag Manager
tag-manager 62 ms /gtag/js?id=G-6J3ELVNTQE(www.googletagmanager.com) 7,951 ms 62
ms
