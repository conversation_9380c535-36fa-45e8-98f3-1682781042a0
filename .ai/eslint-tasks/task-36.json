{"ruleId": "better-tailwindcss/no-unregistered-classes", "filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/toast.tsx", "messages": [{"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: data-[state=open]:animate-in", "line": 36, "column": 5, "nodeType": null, "endLine": 36, "endColumn": 33}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: data-[state=open]:slide-in-from-top-full", "line": 36, "column": 34, "nodeType": null, "endLine": 36, "endColumn": 74}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: data-[state=open]:sm:slide-in-from-bottom-full", "line": 37, "column": 5, "nodeType": null, "endLine": 37, "endColumn": 51}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: data-[state=closed]:animate-out", "line": 38, "column": 5, "nodeType": null, "endLine": 38, "endColumn": 36}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: data-[state=closed]:fade-out-80", "line": 38, "column": 37, "nodeType": null, "endLine": 38, "endColumn": 68}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: data-[state=closed]:slide-out-to-right-full", "line": 39, "column": 5, "nodeType": null, "endLine": 39, "endColumn": 48}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: data-[swipe=end]:animate-out", "line": 40, "column": 5, "nodeType": null, "endLine": 40, "endColumn": 33}]}