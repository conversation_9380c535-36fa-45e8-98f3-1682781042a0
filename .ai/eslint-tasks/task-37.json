{"ruleId": "better-tailwindcss/no-unregistered-classes", "filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/tooltip.tsx", "messages": [{"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: animate-in", "line": 23, "column": 9, "nodeType": null, "endLine": 23, "endColumn": 19}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: fade-in-0", "line": 23, "column": 20, "nodeType": null, "endLine": 23, "endColumn": 29}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: zoom-in-95", "line": 23, "column": 30, "nodeType": null, "endLine": 23, "endColumn": 40}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: data-[state=closed]:animate-out", "line": 25, "column": 9, "nodeType": null, "endLine": 25, "endColumn": 40}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: data-[state=closed]:fade-out-0", "line": 25, "column": 41, "nodeType": null, "endLine": 25, "endColumn": 71}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: data-[state=closed]:zoom-out-95", "line": 26, "column": 9, "nodeType": null, "endLine": 26, "endColumn": 40}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: data-[side=bottom]:slide-in-from-top-2", "line": 27, "column": 9, "nodeType": null, "endLine": 27, "endColumn": 47}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: data-[side=left]:slide-in-from-right-2", "line": 28, "column": 9, "nodeType": null, "endLine": 28, "endColumn": 47}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: data-[side=right]:slide-in-from-left-2", "line": 29, "column": 9, "nodeType": null, "endLine": 29, "endColumn": 47}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: data-[side=top]:slide-in-from-bottom-2", "line": 30, "column": 9, "nodeType": null, "endLine": 30, "endColumn": 47}]}