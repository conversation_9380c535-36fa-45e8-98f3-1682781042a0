{"ruleId": "better-tailwindcss/no-unregistered-classes", "filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/app/[locale]/river-village/page.tsx", "messages": [{"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: prose", "line": 77, "column": 20, "nodeType": null, "endLine": 77, "endColumn": 25}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: prose", "line": 140, "column": 23, "nodeType": null, "endLine": 140, "endColumn": 28}]}