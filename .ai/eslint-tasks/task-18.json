{"ruleId": "@typescript-eslint/no-explicit-any", "filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/OptimizedImage.tsx", "messages": [{"ruleId": "@typescript-eslint/no-explicit-any", "severity": 1, "message": "Unexpected any. Specify a different type.", "line": 39, "column": 66, "nodeType": "TSAnyKeyword", "messageId": "unexpectedAny", "endLine": 39, "endColumn": 69, "suggestions": [{"messageId": "suggestUnknown", "fix": {"range": [1225, 1228], "text": "unknown"}, "desc": "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct."}, {"messageId": "suggestNever", "fix": {"range": [1225, 1228], "text": "never"}, "desc": "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of."}]}, {"ruleId": "@typescript-eslint/no-explicit-any", "severity": 1, "message": "Unexpected any. Specify a different type.", "line": 58, "column": 45, "nodeType": "TSAnyKeyword", "messageId": "unexpectedAny", "endLine": 58, "endColumn": 48, "suggestions": [{"messageId": "suggestUnknown", "fix": {"range": [1859, 1862], "text": "unknown"}, "desc": "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct."}, {"messageId": "suggestNever", "fix": {"range": [1859, 1862], "text": "never"}, "desc": "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of."}]}, {"ruleId": "@typescript-eslint/no-explicit-any", "severity": 1, "message": "Unexpected any. Specify a different type.", "line": 69, "column": 45, "nodeType": "TSAnyKeyword", "messageId": "unexpectedAny", "endLine": 69, "endColumn": 48, "suggestions": [{"messageId": "suggestUnknown", "fix": {"range": [2339, 2342], "text": "unknown"}, "desc": "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct."}, {"messageId": "suggestNever", "fix": {"range": [2339, 2342], "text": "never"}, "desc": "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of."}]}, {"ruleId": "@typescript-eslint/no-explicit-any", "severity": 1, "message": "Unexpected any. Specify a different type.", "line": 98, "column": 43, "nodeType": "TSAnyKeyword", "messageId": "unexpectedAny", "endLine": 98, "endColumn": 46, "suggestions": [{"messageId": "suggestUnknown", "fix": {"range": [3138, 3141], "text": "unknown"}, "desc": "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct."}, {"messageId": "suggestNever", "fix": {"range": [3138, 3141], "text": "never"}, "desc": "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of."}]}, {"ruleId": "@typescript-eslint/no-explicit-any", "severity": 1, "message": "Unexpected any. Specify a different type.", "line": 110, "column": 43, "nodeType": "TSAnyKeyword", "messageId": "unexpectedAny", "endLine": 110, "endColumn": 46, "suggestions": [{"messageId": "suggestUnknown", "fix": {"range": [3577, 3580], "text": "unknown"}, "desc": "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct."}, {"messageId": "suggestNever", "fix": {"range": [3577, 3580], "text": "never"}, "desc": "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of."}]}]}