{"ruleId": "better-tailwindcss/no-unregistered-classes", "filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/dialog.tsx", "messages": [{"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: data-[state=open]:animate-in", "line": 25, "column": 9, "nodeType": null, "endLine": 25, "endColumn": 37}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: data-[state=open]:fade-in-0", "line": 25, "column": 38, "nodeType": null, "endLine": 25, "endColumn": 65}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: data-[state=closed]:animate-out", "line": 26, "column": 9, "nodeType": null, "endLine": 26, "endColumn": 40}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: data-[state=closed]:fade-out-0", "line": 26, "column": 41, "nodeType": null, "endLine": 26, "endColumn": 71}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: data-[state=open]:animate-in", "line": 46, "column": 11, "nodeType": null, "endLine": 46, "endColumn": 39}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: data-[state=open]:fade-in-0", "line": 46, "column": 40, "nodeType": null, "endLine": 46, "endColumn": 67}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: data-[state=open]:zoom-in-95", "line": 47, "column": 11, "nodeType": null, "endLine": 47, "endColumn": 39}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: data-[state=open]:slide-in-from-left-1/2", "line": 47, "column": 40, "nodeType": null, "endLine": 47, "endColumn": 80}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: data-[state=open]:slide-in-from-top-[48%]", "line": 48, "column": 11, "nodeType": null, "endLine": 48, "endColumn": 52}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: data-[state=closed]:animate-out", "line": 49, "column": 11, "nodeType": null, "endLine": 49, "endColumn": 42}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: data-[state=closed]:fade-out-0", "line": 49, "column": 43, "nodeType": null, "endLine": 49, "endColumn": 73}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: data-[state=closed]:zoom-out-95", "line": 50, "column": 11, "nodeType": null, "endLine": 50, "endColumn": 42}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: data-[state=closed]:slide-out-to-left-1/2", "line": 51, "column": 11, "nodeType": null, "endLine": 51, "endColumn": 52}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: data-[state=closed]:slide-out-to-top-[48%]", "line": 52, "column": 11, "nodeType": null, "endLine": 52, "endColumn": 53}]}