{"ruleId": "@typescript-eslint/no-unused-vars", "filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/hooks/use-toast.ts", "messages": [{"ruleId": "@typescript-eslint/no-unused-vars", "severity": 1, "message": "'actionTypes' is assigned a value but only used as a type. Allowed unused vars must match /^_/u.", "line": 18, "column": 7, "nodeType": null, "messageId": "usedOnlyAsType", "endLine": 18, "endColumn": 18}]}