{"ruleId": "no-console", "filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/contexts/gdpr-context.tsx", "messages": [{"ruleId": "no-console", "severity": 1, "message": "Unexpected console statement.", "line": 49, "column": 9, "nodeType": "MemberExpression", "messageId": "unexpected", "endLine": 49, "endColumn": 22, "suggestions": [{"fix": {"range": [1445, 1498], "text": ""}, "messageId": "removeConsole", "data": {"propertyName": "error"}, "desc": "Remove the console.error()."}]}, {"ruleId": "no-console", "severity": 1, "message": "Unexpected console statement.", "line": 157, "column": 3, "nodeType": "MemberExpression", "messageId": "unexpected", "endLine": 157, "endColumn": 14, "suggestions": [{"fix": {"range": [3956, 4003], "text": ""}, "messageId": "removeConsole", "data": {"propertyName": "log"}, "desc": "Remove the console.log()."}]}]}