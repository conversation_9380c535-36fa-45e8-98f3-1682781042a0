{"ruleId": "no-console", "filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/client/BookingButton.tsx", "messages": [{"ruleId": "no-console", "severity": 1, "message": "Unexpected console statement.", "line": 104, "column": 9, "nodeType": "MemberExpression", "messageId": "unexpected", "endLine": 104, "endColumn": 21, "suggestions": [{"fix": {"range": [3581, 3797], "text": ""}, "messageId": "removeConsole", "data": {"propertyName": "warn"}, "desc": "Remove the console.warn()."}]}, {"ruleId": "no-console", "severity": 1, "message": "Unexpected console statement.", "line": 137, "column": 7, "nodeType": "MemberExpression", "messageId": "unexpected", "endLine": 137, "endColumn": 18, "suggestions": [{"fix": {"range": [4657, 4819], "text": ""}, "messageId": "removeConsole", "data": {"propertyName": "log"}, "desc": "Remove the console.log()."}]}]}