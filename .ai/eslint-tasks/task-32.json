{"ruleId": "better-tailwindcss/no-unregistered-classes", "filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/select.tsx", "messages": [{"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: data-[state=open]:animate-in", "line": 79, "column": 11, "nodeType": null, "endLine": 79, "endColumn": 39}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: data-[state=open]:fade-in-0", "line": 79, "column": 40, "nodeType": null, "endLine": 79, "endColumn": 67}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: data-[state=open]:zoom-in-95", "line": 80, "column": 11, "nodeType": null, "endLine": 80, "endColumn": 39}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: data-[state=closed]:animate-out", "line": 81, "column": 11, "nodeType": null, "endLine": 81, "endColumn": 42}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: data-[state=closed]:fade-out-0", "line": 81, "column": 43, "nodeType": null, "endLine": 81, "endColumn": 73}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: data-[state=closed]:zoom-out-95", "line": 82, "column": 11, "nodeType": null, "endLine": 82, "endColumn": 42}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: data-[side=bottom]:slide-in-from-top-2", "line": 83, "column": 11, "nodeType": null, "endLine": 83, "endColumn": 49}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: data-[side=left]:slide-in-from-right-2", "line": 84, "column": 11, "nodeType": null, "endLine": 84, "endColumn": 49}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: data-[side=right]:slide-in-from-left-2", "line": 85, "column": 11, "nodeType": null, "endLine": 85, "endColumn": 49}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: data-[side=top]:slide-in-from-bottom-2", "line": 86, "column": 11, "nodeType": null, "endLine": 86, "endColumn": 49}]}