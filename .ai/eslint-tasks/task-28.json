{"ruleId": "better-tailwindcss/no-unregistered-classes", "filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/menubar.tsx", "messages": [{"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: data-[state=open]:animate-in", "line": 90, "column": 9, "nodeType": null, "endLine": 90, "endColumn": 37}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: data-[state=open]:fade-in-0", "line": 90, "column": 38, "nodeType": null, "endLine": 90, "endColumn": 65}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: data-[state=open]:zoom-in-95", "line": 91, "column": 9, "nodeType": null, "endLine": 91, "endColumn": 37}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: data-[state=closed]:animate-out", "line": 92, "column": 9, "nodeType": null, "endLine": 92, "endColumn": 40}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: data-[state=closed]:fade-out-0", "line": 92, "column": 41, "nodeType": null, "endLine": 92, "endColumn": 71}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: data-[state=closed]:zoom-out-95", "line": 93, "column": 9, "nodeType": null, "endLine": 93, "endColumn": 40}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: data-[side=bottom]:slide-in-from-top-2", "line": 94, "column": 9, "nodeType": null, "endLine": 94, "endColumn": 47}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: data-[side=left]:slide-in-from-right-2", "line": 95, "column": 9, "nodeType": null, "endLine": 95, "endColumn": 47}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: data-[side=right]:slide-in-from-left-2", "line": 96, "column": 9, "nodeType": null, "endLine": 96, "endColumn": 47}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: data-[side=top]:slide-in-from-bottom-2", "line": 97, "column": 9, "nodeType": null, "endLine": 97, "endColumn": 47}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: data-[state=open]:animate-in", "line": 120, "column": 11, "nodeType": null, "endLine": 120, "endColumn": 39}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: data-[state=open]:fade-in-0", "line": 120, "column": 40, "nodeType": null, "endLine": 120, "endColumn": 67}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: data-[state=open]:zoom-in-95", "line": 121, "column": 11, "nodeType": null, "endLine": 121, "endColumn": 39}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: data-[state=closed]:fade-out-0", "line": 122, "column": 11, "nodeType": null, "endLine": 122, "endColumn": 41}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: data-[state=closed]:zoom-out-95", "line": 122, "column": 42, "nodeType": null, "endLine": 122, "endColumn": 73}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: data-[side=bottom]:slide-in-from-top-2", "line": 123, "column": 11, "nodeType": null, "endLine": 123, "endColumn": 49}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: data-[side=left]:slide-in-from-right-2", "line": 124, "column": 11, "nodeType": null, "endLine": 124, "endColumn": 49}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: data-[side=right]:slide-in-from-left-2", "line": 125, "column": 11, "nodeType": null, "endLine": 125, "endColumn": 49}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: data-[side=top]:slide-in-from-bottom-2", "line": 126, "column": 11, "nodeType": null, "endLine": 126, "endColumn": 49}]}