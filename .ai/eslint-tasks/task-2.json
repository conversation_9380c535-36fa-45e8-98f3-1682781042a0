{"ruleId": "better-tailwindcss/no-unregistered-classes", "filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/app/[locale]/kayaking/page.tsx", "messages": [{"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: prose", "line": 46, "column": 23, "nodeType": null, "endLine": 46, "endColumn": 28}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: prose-lg", "line": 46, "column": 29, "nodeType": null, "endLine": 46, "endColumn": 37}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: lead", "line": 47, "column": 23, "nodeType": null, "endLine": 47, "endColumn": 27}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: prose", "line": 76, "column": 23, "nodeType": null, "endLine": 76, "endColumn": 28}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: prose-lg", "line": 76, "column": 29, "nodeType": null, "endLine": 76, "endColumn": 37}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: lead", "line": 77, "column": 23, "nodeType": null, "endLine": 77, "endColumn": 27}]}