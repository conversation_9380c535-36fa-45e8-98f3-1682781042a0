{"ruleId": "better-tailwindcss/no-unregistered-classes", "filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/context-menu.tsx", "messages": [{"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: data-[state=open]:animate-in", "line": 55, "column": 9, "nodeType": null, "endLine": 55, "endColumn": 37}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: data-[state=open]:fade-in-0", "line": 55, "column": 38, "nodeType": null, "endLine": 55, "endColumn": 65}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: data-[state=open]:zoom-in-95", "line": 56, "column": 9, "nodeType": null, "endLine": 56, "endColumn": 37}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: data-[state=closed]:animate-out", "line": 57, "column": 9, "nodeType": null, "endLine": 57, "endColumn": 40}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: data-[state=closed]:fade-out-0", "line": 57, "column": 41, "nodeType": null, "endLine": 57, "endColumn": 71}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: data-[state=closed]:zoom-out-95", "line": 58, "column": 9, "nodeType": null, "endLine": 58, "endColumn": 40}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: data-[side=bottom]:slide-in-from-top-2", "line": 59, "column": 9, "nodeType": null, "endLine": 59, "endColumn": 47}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: data-[side=left]:slide-in-from-right-2", "line": 60, "column": 9, "nodeType": null, "endLine": 60, "endColumn": 47}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: data-[side=right]:slide-in-from-left-2", "line": 61, "column": 9, "nodeType": null, "endLine": 61, "endColumn": 47}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: data-[side=top]:slide-in-from-bottom-2", "line": 62, "column": 9, "nodeType": null, "endLine": 62, "endColumn": 47}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: animate-in", "line": 82, "column": 11, "nodeType": null, "endLine": 82, "endColumn": 21}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: fade-in-80", "line": 82, "column": 22, "nodeType": null, "endLine": 82, "endColumn": 32}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: data-[state=open]:animate-in", "line": 84, "column": 11, "nodeType": null, "endLine": 84, "endColumn": 39}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: data-[state=open]:fade-in-0", "line": 84, "column": 40, "nodeType": null, "endLine": 84, "endColumn": 67}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: data-[state=open]:zoom-in-95", "line": 85, "column": 11, "nodeType": null, "endLine": 85, "endColumn": 39}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: data-[state=closed]:animate-out", "line": 86, "column": 11, "nodeType": null, "endLine": 86, "endColumn": 42}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: data-[state=closed]:fade-out-0", "line": 86, "column": 43, "nodeType": null, "endLine": 86, "endColumn": 73}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: data-[state=closed]:zoom-out-95", "line": 87, "column": 11, "nodeType": null, "endLine": 87, "endColumn": 42}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: data-[side=bottom]:slide-in-from-top-2", "line": 88, "column": 11, "nodeType": null, "endLine": 88, "endColumn": 49}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: data-[side=left]:slide-in-from-right-2", "line": 89, "column": 11, "nodeType": null, "endLine": 89, "endColumn": 49}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: data-[side=right]:slide-in-from-left-2", "line": 90, "column": 11, "nodeType": null, "endLine": 90, "endColumn": 49}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: data-[side=top]:slide-in-from-bottom-2", "line": 91, "column": 11, "nodeType": null, "endLine": 91, "endColumn": 49}]}