{"ruleId": "better-tailwindcss/no-unregistered-classes", "filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/navigation-menu.tsx", "messages": [{"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: data-[motion^=from-]:animate-in", "line": 88, "column": 9, "nodeType": null, "endLine": 88, "endColumn": 40}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: data-[motion^=from-]:fade-in", "line": 88, "column": 41, "nodeType": null, "endLine": 88, "endColumn": 69}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: data-[motion^=to-]:animate-out", "line": 89, "column": 9, "nodeType": null, "endLine": 89, "endColumn": 39}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: data-[motion^=to-]:fade-out", "line": 89, "column": 40, "nodeType": null, "endLine": 89, "endColumn": 67}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: data-[motion=from-end]:slide-in-from-right-52", "line": 90, "column": 9, "nodeType": null, "endLine": 90, "endColumn": 54}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: data-[motion=from-start]:slide-in-from-left-52", "line": 91, "column": 9, "nodeType": null, "endLine": 91, "endColumn": 55}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: data-[motion=to-end]:slide-out-to-right-52", "line": 92, "column": 9, "nodeType": null, "endLine": 92, "endColumn": 51}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: data-[motion=to-start]:slide-out-to-left-52", "line": 93, "column": 9, "nodeType": null, "endLine": 93, "endColumn": 52}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: origin-top-center", "line": 114, "column": 11, "nodeType": null, "endLine": 114, "endColumn": 28}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: data-[state=open]:animate-in", "line": 117, "column": 11, "nodeType": null, "endLine": 117, "endColumn": 39}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: data-[state=open]:zoom-in-90", "line": 117, "column": 40, "nodeType": null, "endLine": 117, "endColumn": 68}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: data-[state=closed]:animate-out", "line": 118, "column": 11, "nodeType": null, "endLine": 118, "endColumn": 42}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: data-[state=closed]:zoom-out-95", "line": 118, "column": 43, "nodeType": null, "endLine": 118, "endColumn": 74}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: data-[state=visible]:animate-in", "line": 138, "column": 9, "nodeType": null, "endLine": 138, "endColumn": 40}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: data-[state=visible]:fade-in", "line": 138, "column": 41, "nodeType": null, "endLine": 138, "endColumn": 69}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: data-[state=hidden]:animate-out", "line": 139, "column": 9, "nodeType": null, "endLine": 139, "endColumn": 40}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: data-[state=hidden]:fade-out", "line": 139, "column": 41, "nodeType": null, "endLine": 139, "endColumn": 69}]}