{"ruleId": "better-tailwindcss/no-unregistered-classes", "filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/EnhancedPackageCard.tsx", "messages": [{"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: mosaic-grid", "line": 161, "column": 15, "nodeType": null, "endLine": 161, "endColumn": 26}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: mosaic-main", "line": 166, "column": 29, "nodeType": null, "endLine": 166, "endColumn": 40}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: mosaic-top", "line": 176, "column": 29, "nodeType": null, "endLine": 176, "endColumn": 39}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: mosaic-bottom", "line": 186, "column": 29, "nodeType": null, "endLine": 186, "endColumn": 42}]}