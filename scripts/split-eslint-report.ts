import fs from 'fs'
import path from 'path'

// Define the structure of a single message from the ESLint report
interface ESLintMessage {
  ruleId: string
  severity: number
  message: string
  line: number
  column: number
  nodeType: string
  messageId: string
  endLine?: number
  endColumn?: number
}

// Define the structure for a file entry in the ESLint report
interface ESLintFileReport {
  filePath: string
  messages: ESLintMessage[]
  errorCount: number
  warningCount: number
  fixableErrorCount: number
  fixableWarningCount: number
  source?: string
}

// Define the structure for a task file
interface Task {
  ruleId: string
  filePath: string
  messages: ESLintMessage[]
}

const report: ESLintFileReport[] = JSON.parse(fs.readFileSync('eslint-report.json', 'utf8'))
const outputDir = path.join(process.cwd(), '.ai', 'eslint-tasks')

if (!fs.existsSync(outputDir)) {
  fs.mkdirSync(outputDir, { recursive: true })
}

const buckets: Record<string, Task> = {}

for (const fileReport of report) {
  if (fileReport.messages.length === 0) {
    continue
  }

  for (const message of fileReport.messages) {
    const key = `${message.ruleId}::${fileReport.filePath}`
    if (!buckets[key]) {
      buckets[key] = {
        ruleId: message.ruleId,
        filePath: fileReport.filePath,
        messages: [],
      }
    }
    buckets[key].messages.push(message)
  }
}

Object.values(buckets).forEach((task, i) => {
  const taskFilePath = path.join(outputDir, `task-${i + 1}.json`)
  fs.writeFileSync(taskFilePath, JSON.stringify(task, null, 2))
})

console.log(`Generated ${Object.keys(buckets).length} task files in ${outputDir}`)
